/**
 * Data Insights JavaScript
 * Phase 3: Data Intelligence & Analytics
 */

(function($) {
    'use strict';

    // Main Data Insights Object
    window.DAB_DataInsights = {

        // Configuration
        config: {
            ajaxUrl: (typeof dab_admin_vars !== 'undefined' && dab_admin_vars.ajaxurl) ? dab_admin_vars.ajaxurl : ajaxurl,
            nonce: (typeof dab_admin_vars !== 'undefined' && dab_admin_vars.nonce) ? dab_admin_vars.nonce : '',
            refreshInterval: 60000, // 1 minute
            autoRefresh: true
        },

        // State management
        state: {
            currentTab: 'insights',
            filters: {},
            insights: [],
            anomalies: [],
            recommendations: [],
            trends: []
        },

        // Initialize the data insights
        init: function() {
            console.log('DAB Data Insights: Initializing...');
            this.bindEvents();
            this.loadInitialData();
            this.initializeComponents();

            // Start auto-refresh if enabled
            if (this.config.autoRefresh) {
                this.startAutoRefresh();
            }
        },

        // Bind event handlers
        bindEvents: function() {
            var self = this;

            // Tab navigation
            $(document).on('click', '.dab-tab-btn', function() {
                var tab = $(this).data('tab');
                self.switchTab(tab);
            });

            // Action buttons
            $(document).on('click', '#generate-insights', function() {
                self.generateInsights();
            });

            $(document).on('click', '#export-insights', function() {
                self.exportInsights();
            });

            $(document).on('click', '#create-custom-insight', function() {
                self.createCustomInsight();
            });

            // Filter controls
            $(document).on('change', '#insight-type-filter', function() {
                self.applyFilters();
            });

            $(document).on('change', '#date-range-filter', function() {
                self.applyFilters();
            });

            $(document).on('change', '#severity-filter', function() {
                self.applyFilters();
            });

            $(document).on('change', '#data-source-filter', function() {
                self.applyFilters();
            });

            // Insight actions
            $(document).on('click', '.dab-insight-action', function() {
                var action = $(this).data('action');
                var insightId = $(this).closest('.dab-insight-card').data('insight-id');
                self.handleInsightAction(action, insightId);
            });

            // Modal close
            $(document).on('click', '.dab-modal-close, .dab-modal-overlay', function() {
                self.closeModal();
            });
        },

        // Initialize components
        initializeComponents: function() {
            this.initializeCharts();
            this.initializeTooltips();
        },

        // Load initial data
        loadInitialData: function() {
            this.loadInsights();
            this.loadSummaryStats();
        },

        // Switch tabs
        switchTab: function(tab) {
            this.state.currentTab = tab;

            // Update tab buttons
            $('.dab-tab-btn').removeClass('active');
            $(`.dab-tab-btn[data-tab="${tab}"]`).addClass('active');

            // Update content
            $('.dab-tab-content').removeClass('active');
            $(`.dab-tab-content[data-tab="${tab}"]`).addClass('active');

            // Load tab-specific data
            this.loadTabData(tab);
        },

        // Load tab-specific data
        loadTabData: function(tab) {
            switch(tab) {
                case 'insights':
                    this.loadInsights();
                    break;
                case 'anomalies':
                    this.loadAnomalies();
                    break;
                case 'recommendations':
                    this.loadRecommendations();
                    break;
                case 'trends':
                    this.loadTrends();
                    break;
            }
        },

        // Generate new insights
        generateInsights: function() {
            var self = this;
            var $button = $('#generate-insights');

            // Show loading state
            var originalText = $button.html();
            $button.html('<span class="dashicons dashicons-update" style="animation: spin 1s linear infinite;"></span> Generating...').prop('disabled', true);

            // Simulate insight generation (replace with actual AJAX call)
            setTimeout(function() {
                self.showSuccess('New insights generated successfully!');
                self.loadInsights();

                // Restore button
                $button.html(originalText).prop('disabled', false);
            }, 2000);
        },

        // Export insights
        exportInsights: function() {
            var self = this;

            // Create export data
            var exportData = {
                insights: this.state.insights,
                anomalies: this.state.anomalies,
                recommendations: this.state.recommendations,
                trends: this.state.trends,
                exported_at: new Date().toISOString()
            };

            // Create and download file
            var dataStr = JSON.stringify(exportData, null, 2);
            var dataBlob = new Blob([dataStr], {type: 'application/json'});
            var url = URL.createObjectURL(dataBlob);

            var link = document.createElement('a');
            link.href = url;
            link.download = 'data-insights-' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showSuccess('Insights exported successfully!');
        },

        // Create custom insight
        createCustomInsight: function() {
            this.showCustomInsightModal();
        },

        // Apply filters
        applyFilters: function() {
            var filters = {
                type: $('#insight-type-filter').val(),
                dateRange: $('#date-range-filter').val(),
                severity: $('#severity-filter').val(),
                dataSource: $('#data-source-filter').val()
            };

            this.state.filters = filters;
            this.loadTabData(this.state.currentTab);
        },

        // Handle insight actions
        handleInsightAction: function(action, insightId) {
            switch(action) {
                case 'view':
                    this.viewInsightDetails(insightId);
                    break;
                case 'dismiss':
                    this.dismissInsight(insightId);
                    break;
                case 'bookmark':
                    this.bookmarkInsight(insightId);
                    break;
                case 'share':
                    this.shareInsight(insightId);
                    break;
            }
        },

        // Load insights
        loadInsights: function() {
            var self = this;

            // Show loading
            $('#insights-content').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading insights...</p></div>');

            // Simulate loading (replace with actual AJAX call)
            setTimeout(function() {
                self.renderInsights();
            }, 1000);
        },

        // Load anomalies
        loadAnomalies: function() {
            var self = this;

            // Show loading
            $('#anomalies-content').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading anomalies...</p></div>');

            // Simulate loading
            setTimeout(function() {
                self.renderAnomalies();
            }, 1000);
        },

        // Load recommendations
        loadRecommendations: function() {
            var self = this;

            // Show loading
            $('#recommendations-content').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading recommendations...</p></div>');

            // Simulate loading
            setTimeout(function() {
                self.renderRecommendations();
            }, 1000);
        },

        // Load trends
        loadTrends: function() {
            var self = this;

            // Show loading
            $('#trends-content').html('<div class="dab-loading"><div class="spinner is-active"></div><p>Loading trends...</p></div>');

            // Simulate loading
            setTimeout(function() {
                self.renderTrends();
            }, 1000);
        },

        // Load summary statistics
        loadSummaryStats: function() {
            // Simulate loading stats
            $('#active-insights').text('12');
            $('#critical-anomalies').text('3');
            $('#optimization-opportunities').text('8');
            $('#data-quality-score').text('87%');
        },

        // Render insights
        renderInsights: function() {
            var html = this.generateMockInsights();
            $('#insights-content').html(html);
        },

        // Render anomalies
        renderAnomalies: function() {
            var html = this.generateMockAnomalies();
            $('#anomalies-content').html(html);
        },

        // Render recommendations
        renderRecommendations: function() {
            var html = this.generateMockRecommendations();
            $('#recommendations-content').html(html);
        },

        // Render trends
        renderTrends: function() {
            var html = this.generateMockTrends();
            $('#trends-content').html(html);
        },

        // Generate mock insights
        generateMockInsights: function() {
            var insights = [
                {
                    id: 1,
                    title: 'High User Engagement Detected',
                    description: 'User activity has increased by 45% in the last 7 days',
                    type: 'positive',
                    severity: 'medium',
                    source: 'User Analytics',
                    timestamp: '2 hours ago'
                },
                {
                    id: 2,
                    title: 'Data Quality Issue Found',
                    description: 'Missing values detected in 12% of recent records',
                    type: 'warning',
                    severity: 'high',
                    source: 'Data Validation',
                    timestamp: '4 hours ago'
                },
                {
                    id: 3,
                    title: 'Performance Optimization Opportunity',
                    description: 'Database queries can be optimized for 30% faster response',
                    type: 'optimization',
                    severity: 'medium',
                    source: 'Performance Monitor',
                    timestamp: '1 day ago'
                }
            ];

            var html = '<div class="dab-insights-grid">';
            insights.forEach(function(insight) {
                html += '<div class="dab-insight-card" data-insight-id="' + insight.id + '">';
                html += '<div class="dab-insight-header">';
                html += '<div class="dab-insight-type dab-type-' + insight.type + '"></div>';
                html += '<div class="dab-insight-severity dab-severity-' + insight.severity + '">' + insight.severity.toUpperCase() + '</div>';
                html += '</div>';
                html += '<div class="dab-insight-content">';
                html += '<h4>' + insight.title + '</h4>';
                html += '<p>' + insight.description + '</p>';
                html += '<div class="dab-insight-meta">';
                html += '<span class="dab-insight-source">' + insight.source + '</span>';
                html += '<span class="dab-insight-time">' + insight.timestamp + '</span>';
                html += '</div>';
                html += '</div>';
                html += '<div class="dab-insight-actions">';
                html += '<button class="button dab-insight-action" data-action="view">View Details</button>';
                html += '<button class="button dab-insight-action" data-action="dismiss">Dismiss</button>';
                html += '</div>';
                html += '</div>';
            });
            html += '</div>';

            return html;
        },

        // Generate mock anomalies
        generateMockAnomalies: function() {
            var anomalies = [
                {
                    id: 1,
                    title: 'Unusual Spike in Error Rates',
                    description: 'Error rate increased from 2% to 15% in the last hour',
                    severity: 'critical',
                    detected: '30 minutes ago'
                },
                {
                    id: 2,
                    title: 'Abnormal Data Pattern',
                    description: 'Unexpected data distribution in recent submissions',
                    severity: 'medium',
                    detected: '2 hours ago'
                }
            ];

            var html = '<div class="dab-anomalies-list">';
            anomalies.forEach(function(anomaly) {
                html += '<div class="dab-anomaly-card dab-severity-' + anomaly.severity + '">';
                html += '<div class="dab-anomaly-header">';
                html += '<h4>' + anomaly.title + '</h4>';
                html += '<span class="dab-anomaly-severity">' + anomaly.severity.toUpperCase() + '</span>';
                html += '</div>';
                html += '<p>' + anomaly.description + '</p>';
                html += '<div class="dab-anomaly-meta">Detected: ' + anomaly.detected + '</div>';
                html += '</div>';
            });
            html += '</div>';

            return html;
        },

        // Generate mock recommendations
        generateMockRecommendations: function() {
            var recommendations = [
                {
                    id: 1,
                    title: 'Optimize Database Indexes',
                    description: 'Adding indexes to frequently queried columns could improve performance by 40%',
                    impact: 'high',
                    effort: 'medium'
                },
                {
                    id: 2,
                    title: 'Implement Data Validation Rules',
                    description: 'Add validation rules to prevent data quality issues',
                    impact: 'medium',
                    effort: 'low'
                }
            ];

            var html = '<div class="dab-recommendations-list">';
            recommendations.forEach(function(rec) {
                html += '<div class="dab-recommendation-card">';
                html += '<h4>' + rec.title + '</h4>';
                html += '<p>' + rec.description + '</p>';
                html += '<div class="dab-recommendation-meta">';
                html += '<span class="dab-impact dab-impact-' + rec.impact + '">Impact: ' + rec.impact.toUpperCase() + '</span>';
                html += '<span class="dab-effort dab-effort-' + rec.effort + '">Effort: ' + rec.effort.toUpperCase() + '</span>';
                html += '</div>';
                html += '</div>';
            });
            html += '</div>';

            return html;
        },

        // Generate mock trends
        generateMockTrends: function() {
            var html = '<div class="dab-trends-container">';
            html += '<div class="dab-trend-chart">';
            html += '<h4>Data Growth Trend</h4>';
            html += '<div id="trend-chart" style="height: 300px; background: #f9f9f9; display: flex; align-items: center; justify-content: center; color: #666;">Chart will be rendered here</div>';
            html += '</div>';
            html += '</div>';

            return html;
        },

        // Show custom insight modal
        showCustomInsightModal: function() {
            var modalHtml = '<div class="dab-modal-overlay">';
            modalHtml += '<div class="dab-modal">';
            modalHtml += '<div class="dab-modal-header">';
            modalHtml += '<h3>Create Custom Insight</h3>';
            modalHtml += '<button class="dab-modal-close">&times;</button>';
            modalHtml += '</div>';
            modalHtml += '<div class="dab-modal-body">';
            modalHtml += '<form id="custom-insight-form">';
            modalHtml += '<div class="dab-form-group">';
            modalHtml += '<label>Insight Title</label>';
            modalHtml += '<input type="text" id="insight-title" class="dab-input" required>';
            modalHtml += '</div>';
            modalHtml += '<div class="dab-form-group">';
            modalHtml += '<label>Description</label>';
            modalHtml += '<textarea id="insight-description" class="dab-textarea" rows="3" required></textarea>';
            modalHtml += '</div>';
            modalHtml += '<div class="dab-form-row">';
            modalHtml += '<div class="dab-form-group">';
            modalHtml += '<label>Type</label>';
            modalHtml += '<select id="insight-type" class="dab-select">';
            modalHtml += '<option value="positive">Positive</option>';
            modalHtml += '<option value="warning">Warning</option>';
            modalHtml += '<option value="optimization">Optimization</option>';
            modalHtml += '</select>';
            modalHtml += '</div>';
            modalHtml += '<div class="dab-form-group">';
            modalHtml += '<label>Severity</label>';
            modalHtml += '<select id="insight-severity" class="dab-select">';
            modalHtml += '<option value="low">Low</option>';
            modalHtml += '<option value="medium">Medium</option>';
            modalHtml += '<option value="high">High</option>';
            modalHtml += '</select>';
            modalHtml += '</div>';
            modalHtml += '</div>';
            modalHtml += '<div class="dab-form-actions">';
            modalHtml += '<button type="button" class="button" id="cancel-insight">Cancel</button>';
            modalHtml += '<button type="submit" class="button button-primary">Create Insight</button>';
            modalHtml += '</div>';
            modalHtml += '</form>';
            modalHtml += '</div>';
            modalHtml += '</div>';
            modalHtml += '</div>';

            $('body').append(modalHtml);

            // Bind modal events
            var self = this;
            $('#cancel-insight, .dab-modal-close').on('click', function() {
                self.closeModal();
            });

            $('#custom-insight-form').on('submit', function(e) {
                e.preventDefault();
                self.saveCustomInsight();
            });
        },

        // Save custom insight
        saveCustomInsight: function() {
            var title = $('#insight-title').val();
            var description = $('#insight-description').val();
            var type = $('#insight-type').val();
            var severity = $('#insight-severity').val();

            if (title && description) {
                this.showSuccess('Custom insight created successfully!');
                this.closeModal();
                this.loadInsights();
            }
        },

        // Close modal
        closeModal: function() {
            $('.dab-modal-overlay').remove();
        },

        // Initialize charts
        initializeCharts: function() {
            // Chart initialization would go here
        },

        // Initialize tooltips
        initializeTooltips: function() {
            // Tooltip initialization would go here
        },

        // Start auto-refresh
        startAutoRefresh: function() {
            var self = this;
            setInterval(function() {
                self.loadSummaryStats();
            }, this.config.refreshInterval);
        },

        // Show success message
        showSuccess: function(message) {
            var notice = $('<div class="notice notice-success is-dismissible"><p>' + message + '</p></div>');
            $('.dab-insights-page h1').after(notice);
            setTimeout(function() {
                notice.fadeOut();
            }, 3000);
        },

        // Show error message
        showError: function(message) {
            var notice = $('<div class="notice notice-error is-dismissible"><p>' + message + '</p></div>');
            $('.dab-insights-page h1').after(notice);
            setTimeout(function() {
                notice.fadeOut();
            }, 5000);
        }
    };

})(jQuery);
